import '../models/api_response.dart';
import '../models/gewe_message.dart';
import '../network/base_service.dart';
import 'package:logger/logger.dart';

final logger = Logger();

/// GeWe 消息服务
class GeWeMessageService extends BaseService {
  /// 发送消息
  /// [request] 发送消息请求
  Future<ApiResponse<SendMessageResponse>> sendMessage(
    SendMessageRequest request,
  ) async {
    return await post<SendMessageResponse>(
      '/gewe/friend/message/send',
      data: request.toJson(),
      fromJson:
          (json) => SendMessageResponse.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 发送好友消息
  /// [toWxId] 接收者微信ID
  /// [content] 消息内容
  Future<ApiResponse<SendMessageResponse>> sendFriendMessage({
    required String toWxId,
    required String content,
  }) async {
    final request = SendMessageRequest.friend(toWxId: toWxId, content: content);
    return await sendMessage(request);
  }

  /// 发送群聊消息
  /// [toWxId] 群聊微信ID
  /// [content] 消息内容
  /// [atWxIds] @的用户微信ID列表
  Future<ApiResponse<SendMessageResponse>> sendGroupMessage({
    required String toWxId,
    required String content,
    List<String>? atWxIds,
  }) async {
    final request = SendMessageRequest.group(
      toWxId: toWxId,
      content: content,
      atWxIds: atWxIds?.join(','),
    );
    return await sendMessage(request);
  }

  /// 获取聊天历史记录
  /// [wxId1] 用户1微信ID
  /// [wxId2] 用户2微信ID
  /// [page] 页码，从0开始
  /// [size] 每页大小
  Future<ApiResponse<PageResponse<GeWeMessage>>> getChatHistory({
    required String wxId1,
    required String wxId2,
    int page = 0,
    int size = 20,
  }) async {
    final raw = await get<ApiResponse<PageResponse<GeWeMessage>>>(
      '/gewe/friend/message/history/chat',
      queryParameters: {
        'wxId1': wxId1,
        'wxId2': wxId2,
        'page': page,
        'size': size,
      },
      fromJson: (json) =>
          ApiResponse<PageResponse<GeWeMessage>>.fromJson(
            json as Map<String, dynamic>,
            (pageJson) => PageResponse<GeWeMessage>.fromJson(
              pageJson as Map<String, dynamic>,
              (messageJson) => GeWeMessage.fromJson(messageJson as Map<String, dynamic>),
            ),
          ),
    );

    // 手动封装 ApiResponse<PageResponse<GeWeMessage>>
    final response = ApiResponse<PageResponse<GeWeMessage>>(
      code: raw.code,
      message: raw.message,
      data:
          raw.data == null
              ? null
              : PageResponse<GeWeMessage>.fromJson(
                raw.data as Map<String, dynamic>,
                (item) => GeWeMessage.fromJson(item as Map<String, dynamic>),
              ),
    );

    print("response 123 : ${response}");
    return response;
  }

  /// 获取群聊历史记录
  ///
  /// [groupWxId] 群聊微信ID
  /// [page] 页码，从0开始
  /// [size] 每页大小
  Future<ApiResponse<PageResponse<GeWeMessage>>> getGroupChatHistory({
    required String groupWxId,
    int page = 0,
    int size = 20,
  }) async {
    logger.d('getGroupChatHistory');
    final raw = await get<PageResponse<GeWeMessage>>(
      '/gewe/friend/message/history/group',
      queryParameters: {'groupWxId': groupWxId, 'page': page, 'size': size},
      fromJson:
          (json) => PageResponse<GeWeMessage>.fromJson(
            json as Map<String, dynamic>,
            (item) => GeWeMessage.fromJson(item as Map<String, dynamic>),
          ),
    );

    // 手动封装 ApiResponse<PageResponse<GeWeMessage>>
    return ApiResponse<PageResponse<GeWeMessage>>(
      code: raw.code,
      message: raw.message,
      data:
          raw.data == null
              ? null
              : PageResponse<GeWeMessage>.fromJson(
                raw.data as Map<String, dynamic>,
                (item) => GeWeMessage.fromJson(item as Map<String, dynamic>),
              ),
    );
  }

  /// 获取最近联系人
  /// [wxId] 用户微信ID
  /// [limit] 返回数量限制
  Future<ApiResponse<RecentContactsResponse>> getRecentContacts({
    required String wxId,
    int limit = 10,
  }) async {
    return await get<RecentContactsResponse>(
      '/gewe/friend/message/recent/contacts',
      queryParameters: {'wxId': wxId, 'limit': limit},
      fromJson:
          (json) =>
              RecentContactsResponse.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 测试消息回调
  Future<ApiResponse<Map<String, dynamic>>> testMessageCallback() async {
    return await post<Map<String, dynamic>>(
      '/gewe/friend/message/callback/test',
      fromJson: (json) => json as Map<String, dynamic>,
    );
  }

  /// 获取与指定联系人的聊天记录（自动判断是好友还是群聊）
  /// [currentUserWxId] 当前用户微信ID
  /// [contactWxId] 联系人微信ID
  /// [page] 页码
  /// [size] 每页大小
  Future<ApiResponse<PageResponse<GeWeMessage>>> getChatHistoryWithContact({
    required String currentUserWxId,
    required String contactWxId,
    int page = 0,
    int size = 20,
  }) async {
    // 判断是否为群聊（群聊ID通常以@chatroom结尾）
    if (contactWxId.endsWith('@chatroom')) {
      return await getGroupChatHistory(
        groupWxId: contactWxId,
        page: page,
        size: size,
      );
    } else {
      return await getChatHistory(
        wxId1: currentUserWxId,
        wxId2: contactWxId,
        page: page,
        size: size,
      );
    }
  }

  /// 发送消息到指定联系人（自动判断是好友还是群聊）
  /// [toWxId] 接收者微信ID
  /// [content] 消息内容
  /// [atWxIds] @的用户微信ID列表（仅群聊有效）
  Future<ApiResponse<SendMessageResponse>> sendMessageToContact({
    required String toWxId,
    required String content,
    List<String>? atWxIds,
  }) async {
    // 判断是否为群聊
    if (toWxId.endsWith('@chatroom')) {
      return await sendGroupMessage(
        toWxId: toWxId,
        content: content,
        atWxIds: atWxIds,
      );
    } else {
      return await sendFriendMessage(toWxId: toWxId, content: content);
    }
  }
}
