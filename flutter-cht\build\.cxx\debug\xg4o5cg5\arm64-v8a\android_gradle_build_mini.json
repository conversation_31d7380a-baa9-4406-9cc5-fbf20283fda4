{"buildFiles": ["F:\\Software\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\new\\flutter-cht\\build\\.cxx\\debug\\xg4o5cg5\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\new\\flutter-cht\\build\\.cxx\\debug\\xg4o5cg5\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}