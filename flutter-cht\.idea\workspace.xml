<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="57d5696d-9431-4697-ac7b-07be90e72457" name="Changes" comment="refactor(models): 扩展成功状态码定义并更新相关功能- 在 ApiResponse 类中扩展了成功状态码定义，支持 200 和 0&#10;- 更新了 AuthInterceptor 中的授权令牌&#10;- 优化了 FriendsPage 中的好友列表处理逻辑&#10;- 调整了 GeweMessageService 中的消息历史记录获取方法">
      <change beforePath="$PROJECT_DIR$/lib/models/gewe_friend.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/models/gewe_friend.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/models/gewe_friend.g.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/models/gewe_friend.g.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/models/gewe_message.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/models/gewe_message.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/models/gewe_message.g.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/models/gewe_message.g.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/chat_page.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/chat_page.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/services/gewe_message_service.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/services/gewe_message_service.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/widgets/message_bubble.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/widgets/message_bubble.dart" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="31y5mH6UHdPWZP9YJYXsq5Vtc2v" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Flutter.main.dart.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "com.google.services.firebase.aqiPopupShown": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "master",
    "io.flutter.reload.alreadyRun": "true",
    "last_opened_file_path": "F:/new/flutter-cht",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="main.dart" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/lib/main.dart" />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="57d5696d-9431-4697-ac7b-07be90e72457" name="Changes" comment="" />
      <created>1756483629962</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756483629962</updated>
    </task>
    <task id="LOCAL-00001" summary="test">
      <option name="closed" value="true" />
      <created>1756515188309</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1756515188309</updated>
    </task>
    <task id="LOCAL-00002" summary="refactor(models): 扩展成功状态码定义并更新相关功能- 在 ApiResponse 类中扩展了成功状态码定义，支持 200 和 0&#10;- 更新了 AuthInterceptor 中的授权令牌&#10;- 优化了 FriendsPage 中的好友列表处理逻辑&#10;- 调整了 GeweMessageService 中的消息历史记录获取方法">
      <option name="closed" value="true" />
      <created>1756518723644</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1756518723644</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="test" />
    <MESSAGE value="refactor(models): 扩展成功状态码定义并更新相关功能- 在 ApiResponse 类中扩展了成功状态码定义，支持 200 和 0&#10;- 更新了 AuthInterceptor 中的授权令牌&#10;- 优化了 FriendsPage 中的好友列表处理逻辑&#10;- 调整了 GeweMessageService 中的消息历史记录获取方法" />
    <option name="LAST_COMMIT_MESSAGE" value="refactor(models): 扩展成功状态码定义并更新相关功能- 在 ApiResponse 类中扩展了成功状态码定义，支持 200 和 0&#10;- 更新了 AuthInterceptor 中的授权令牌&#10;- 优化了 FriendsPage 中的好友列表处理逻辑&#10;- 调整了 GeweMessageService 中的消息历史记录获取方法" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <default-breakpoints>
        <breakpoint type="dart-exception" />
      </default-breakpoints>
    </breakpoint-manager>
  </component>
</project>