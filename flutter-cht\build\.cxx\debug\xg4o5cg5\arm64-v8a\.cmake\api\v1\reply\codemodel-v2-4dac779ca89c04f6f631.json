{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "F:/new/flutter-cht/build/.cxx/debug/xg4o5cg5/arm64-v8a", "source": "F:/Software/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}