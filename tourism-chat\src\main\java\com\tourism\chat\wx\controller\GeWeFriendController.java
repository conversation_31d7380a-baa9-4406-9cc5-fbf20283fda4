package com.tourism.chat.wx.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.chat.common.response.R;
import com.tourism.chat.wx.entity.GeWeFriend;
import com.tourism.chat.wx.entity.GeWeMessage;
import com.tourism.chat.wx.model.WeChatMessage;
import com.tourism.chat.wx.request.GeWeMessageCallbackRequest;
import com.tourism.chat.wx.request.SendMessageRequest;
import com.tourism.chat.wx.request.SyncFriendsRequest;
import com.tourism.chat.wx.service.GeWeFriendSyncService;
import com.tourism.chat.wx.service.GeWeMessageCallbackService;
import com.tourism.chat.wx.service.GeWeMessageHistoryService;
import com.tourism.chat.wx.service.GeWeMessageService;
import com.tourism.chat.wx.vo.GeWeFriendVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * GeWe好友管理控制器
 */
@RestController
@RequestMapping("/gewe/friend")
@Tag(name = "GeWe好友管理接口")
@Slf4j
public class GeWeFriendController {

    @Resource
    private GeWeFriendSyncService geWeFriendSyncService;

    @Resource
    private GeWeMessageService geWeMessageService;

    @Resource
    private GeWeMessageHistoryService geWeMessageHistoryService;

    @Resource
    private GeWeMessageCallbackService geWeMessageCallbackService;

    @Operation(summary = "同步好友信息", description = "从GeWe API同步好友信息到本地数据库")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "同步成功"),
            @ApiResponse(responseCode = "500", description = "同步失败")
    })
    @PostMapping("/sync")
    public R<Map<String, Object>> syncFriends(@RequestBody(required = false) SyncFriendsRequest request) {
        try {
            int syncCount;

            if (request == null || CollectionUtils.isEmpty(request.getWxIds())) {
                // 同步所有好友
                syncCount = geWeFriendSyncService.syncAllFriends();
            } else {
                // 同步指定好友
                syncCount = geWeFriendSyncService.syncFriendsByWxIds(request.getWxIds());
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("syncCount", syncCount);
            result.put("message", "同步完成，共同步 " + syncCount + " 个好友");

            return R.ok(result);

        } catch (Exception e) {
            log.error("同步好友信息失败", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());

            return R.status(500).body(result);
        }
    }

    @Operation(summary = "获取好友列表", description = "获取所有好友列表（不包含群聊）")
    @GetMapping("/list")
    public R<List<GeWeFriendVO>> getFriendList() {
        List<GeWeFriend> friends = geWeFriendSyncService.getAllFriends();
        List<GeWeFriendVO> friendVOs = friends.stream()
                .map(GeWeFriendVO::from)
                .collect(Collectors.toList());

        return R.ok(friendVOs);
    }

    @Operation(summary = "获取群聊列表", description = "获取所有群聊列表")
    @GetMapping("/groups")
    public R<List<GeWeFriendVO>> getGroupList() {
        List<GeWeFriend> groups = geWeFriendSyncService.getAllGroups();
        List<GeWeFriendVO> groupVOs = groups.stream()
                .map(GeWeFriendVO::from)
                .collect(Collectors.toList());

        return R.ok(groupVOs);
    }

    @Operation(summary = "搜索好友", description = "根据关键词搜索好友（昵称或备注）")
    @GetMapping("/search")
    public R<List<GeWeFriendVO>> searchFriends(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {

        List<GeWeFriend> friends = geWeFriendSyncService.searchFriends(keyword);
        List<GeWeFriendVO> friendVOs = friends.stream()
                .map(GeWeFriendVO::from)
                .collect(Collectors.toList());

        return R.ok(friendVOs);
    }

    @Operation(summary = "获取好友详情", description = "根据微信ID获取好友详细信息")
    @GetMapping("/{wxId}")
    public R<GeWeFriendVO> getFriendDetail(
            @Parameter(description = "微信ID") @PathVariable String wxId) {

        GeWeFriend friend = geWeFriendSyncService.getFriendByWxId(wxId);

        if (friend == null) {
            return R.fail("not found");
        }

        return R.ok(GeWeFriendVO.from(friend));
    }

    @Operation(summary = "刷新好友信息", description = "从GeWe API重新获取指定好友的最新信息")
    @PostMapping("/{wxId}/refresh")
    public R<Map<String, Object>> refreshFriend(
            @Parameter(description = "微信ID") @PathVariable String wxId) {

        try {
            GeWeFriend friend = geWeFriendSyncService.refreshFriend(wxId);

            Map<String, Object> result = new HashMap<>();
            if (friend != null) {
                result.put("success", true);
                result.put("friend", GeWeFriendVO.from(friend));
                result.put("message", "刷新成功");
            } else {
                result.put("success", false);
                result.put("message", "刷新失败，无法获取好友信息");
            }

            return R.ok(result);

        } catch (Exception e) {
            log.error("刷新好友信息失败: {}", wxId, e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "刷新失败：" + e.getMessage());

            return R.status(500).body(result);
        }
    }

    @Operation(summary = "发送消息", description = "向指定好友或群聊发送文字消息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "发送成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "好友不存在"),
            @ApiResponse(responseCode = "500", description = "发送失败")
    })
    @PostMapping("/message/send")
    public R<Map<String, Object>> sendMessage(@Valid @RequestBody SendMessageRequest request) {
        try {
            boolean success = false;
            String fromWxId = "wxid_xpih49zdmi5a22"; // 这里应该从当前登录用户获取，暂时使用固定值

            if ("group".equals(request.getMessageType())) {
                WeChatMessage message = null;
                // 发送群聊消息
                if (StringUtils.hasText(request.getAtWxIds())) {
                    message = geWeMessageService.sendTextToGroupWithMultipleAt(
                            request.getToWxId(), request.getContent(), request.getAtWxIds());
                } else {
                    message = geWeMessageService.sendTextToGroup(
                            request.getToWxId(), request.getContent());
                }

                // 保存群聊消息到数据库
                if (message != null) {
                    geWeMessageHistoryService.saveSentMessage(
                            fromWxId, request.getToWxId(), request.getContent(),
                            1, true, request.getAtWxIds(), message);
                }
            } else {
                // 发送好友消息
                WeChatMessage message = geWeMessageService.sendTextToFriend(
                        request.getToWxId(), request.getContent());

                // 保存好友消息到数据库
                if (message != null) {
                    success = true;
                    geWeMessageHistoryService.saveSentMessage(
                            fromWxId, request.getToWxId(), request.getContent(),
                            1, false, null, message);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "消息发送成功" : "消息发送失败");

            return R.ok(result);

        } catch (Exception e) {
            log.error("发送消息失败", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "发送失败：" + e.getMessage());

            return R.status(500).body(result);
        }
    }

    @Operation(summary = "获取聊天历史记录", description = "获取两个用户之间的聊天历史记录")
    @GetMapping("/message/history/chat")
    public R<Page<GeWeMessage>> getChatHistory(
            @Parameter(description = "用户1微信ID") @RequestParam String wxId1,
            @Parameter(description = "用户2微信ID") @RequestParam String wxId2,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {

        try {
            wxId1 = "wxid_xpih49zdmi5a22";
            log.info("查询聊天历史: wxId1={}, wxId2={}, page={}, size={}", wxId1, wxId2, page, size);
            Pageable pageable = PageRequest.of(page, size);

            Page<GeWeMessage> messages = geWeMessageHistoryService.getChatHistory(wxId1, wxId2, pageable);
            log.info("查询到 {} 条消息，总页数: {}", messages.getRecords().size(), messages.getTotal());
            return R.ok(messages);
        } catch (Exception e) {
            log.error("获取聊天历史失败", e);
            return R.fail(500);
        }
    }

    @Operation(summary = "获取群聊历史记录", description = "获取群聊的历史消息记录")
    @GetMapping("/message/history/group")
    public R<Page<GeWeMessage>> getGroupChatHistory(
            @Parameter(description = "群聊微信ID") @RequestParam String groupWxId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<GeWeMessage> messages = geWeMessageHistoryService.getGroupChatHistory(groupWxId, pageable);
            return R.ok(messages);
        } catch (Exception e) {
            log.error("获取群聊历史失败", e);
            return R.fail(500, "获取群聊历史失败");
        }
    }

    @Operation(summary = "获取最近联系人", description = "获取最近聊天的联系人列表")
    @GetMapping("/message/recent/contacts")
    public R<Map<String, Object>> getRecentContacts(
            @Parameter(description = "用户微信ID") @RequestParam String wxId,
            @Parameter(description = "返回数量限制") @RequestParam(defaultValue = "10") int limit) {

        try {
            List<String> recentChats = geWeMessageHistoryService.getRecentChatContacts(wxId, limit);
            List<String> recentGroups = geWeMessageHistoryService.getRecentGroups(wxId, limit);

            Map<String, Object> result = new HashMap<>();
            result.put("recentChats", recentChats);
            result.put("recentGroups", recentGroups);

            return R.ok(result);
        } catch (Exception e) {
            log.error("获取最近联系人失败", e);
            return R.status(500);
        }
    }

    @Operation(summary = "消息回调接口", description = "接收GeWe API的消息回调")
    @PostMapping("/message/callback")
    public R<Map<String, Object>> messageCallback(@RequestBody GeWeMessageCallbackRequest request) {
        try {
            log.info("收到消息回调: typeName={}, msgId={}, from={}, to={}",
                    request.getTypeName(), request.getMsgId(), request.getFromWxId(), request.getToWxId());

            // 处理消息回调
            geWeMessageCallbackService.handleMessageCallback(request);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "回调处理成功");

            return R.ok(result);

        } catch (Exception e) {
            log.error("处理消息回调失败", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "回调处理失败：" + e.getMessage());

            return R.status(500).body(result);
        }
    }

    @Operation(summary = "测试GeWe消息回调", description = "用于测试GeWe消息回调处理")
    @PostMapping("/message/callback/test")
    public R<Map<String, Object>> testMessageCallback() {
        try {
            // 创建测试回调数据
            GeWeMessageCallbackRequest testRequest = new GeWeMessageCallbackRequest();
            testRequest.setTypeName("AddMsg");
            testRequest.setAppid("test_app_id");
            testRequest.setWxid("test_wx_id");

            GeWeMessageCallbackRequest.MessageData data = new GeWeMessageCallbackRequest.MessageData();
            data.setMsgId(System.currentTimeMillis());

            GeWeMessageCallbackRequest.StringWrapper fromUser = new GeWeMessageCallbackRequest.StringWrapper();
            fromUser.setString("test_from_user");
            data.setFromUserName(fromUser);

            GeWeMessageCallbackRequest.StringWrapper toUser = new GeWeMessageCallbackRequest.StringWrapper();
            toUser.setString("test_to_user");
            data.setToUserName(toUser);

            data.setMsgType(1); // 文本消息

            GeWeMessageCallbackRequest.StringWrapper content = new GeWeMessageCallbackRequest.StringWrapper();
            content.setString("这是一条测试消息");
            data.setContent(content);

            data.setCreateTime(System.currentTimeMillis() / 1000); // 秒级时间戳
            data.setPushContent("测试用户: 这是一条测试消息");

            testRequest.setData(data);

            log.info("发送测试回调: {}", testRequest);

            // 处理测试回调
            geWeMessageCallbackService.handleMessageCallback(testRequest);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "测试回调处理成功");
            result.put("testData", testRequest);

            return R.ok(result);

        } catch (Exception e) {
            log.error("测试回调处理失败", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "测试回调处理失败：" + e.getMessage());

            return R.status(500).body(result);
        }
    }
}