POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

<> 2025-08-30T095100.200.json

###

GET http://localhost:8080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWQiOjEsImlhdCI6MTc1NjUxNDAwNywiZXhwIjoxNzU2NTE3NjA3fQ.xqlNra4RXQMuZBwCiJG7aHeorSRnsuBVox2pz6IS5p0
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

<> 2025-08-30T084638.200.json

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

<> 2025-08-30T083327.200.json

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=F4993A8063520584E39702635989E1AA

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=1CDE60DD9FAE137458297F3754D9FB11

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:8080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://*************:8080/api/gewe/friend/sync
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=4E505D3FF2DE16DD26EA0ABF734A34EC
content-length: 0

###

POST http://*************:8080/api/gewe/friend/sync
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
content-length: 0

###

GET http://localhost:8080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

<> 2025-08-29T235253.200.json

###

POST http://localhost:8080/api/gewe/friend/sync
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F
content-length: 0

<> 2025-08-29T235235.200.json

###

POST http://localhost:8080/api/gewe/friend/sync
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F
content-length: 0

<> 2025-08-29T234849.200.json

###

POST http://localhost:8080/api/gewe/friend/sync
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F
content-length: 0

<> 2025-08-29T234448.200.json

###

GET http://localhost:8080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

<> 2025-08-29T234442.200.json

###

GET http://localhost:8080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

<> 2025-08-29T232520.200.json

###

POST http://localhost:8080/api/gewe/friend/sync
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F
content-length: 0

<> 2025-08-29T232516.200.json

###

GET http://localhost:8080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

<> 2025-08-29T232410.200.json

###

GET http://localhost:8080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

<> 2025-08-29T231704.200.json

###

GET http://localhost:8080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

<> 2025-08-29T230555.500.json

###

GET http://localhost:18080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDc5NzI5LCJleHAiOjE3NTY0ODMzMjl9.Qr3Fe6-mpwolGY_MwhcxRykUob8mslCVrcrvrCG_TKE
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

<> 2025-08-29T230228.500.json

###

GET http://localhost:18080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDcxOTM0LCJleHAiOjE3NTY0NzU1MzR9.Ip6u5QU_u3vx8BP7SPHNt8zu0ItQWJallu6nYxoyGiA
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

###

POST http://localhost:18080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

<> 2025-08-29T230209.200.json

###

GET http://localhost:18080/api/gewe/friend/list
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDcxOTM0LCJleHAiOjE3NTY0NzU1MzR9.Ip6u5QU_u3vx8BP7SPHNt8zu0ItQWJallu6nYxoyGiA
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=8181746B2205C3097A9CEDDFF717F98F

###

GET http://localhost:18080/api/gewe/friend/list
Content-Type: application/json
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=49A541192751890E2FDAD50AA7E2C2F3

###

GET http://localhost:18080/api/users/1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDcxOTM0LCJleHAiOjE3NTY0NzU1MzR9.Ip6u5QU_u3vx8BP7SPHNt8zu0ItQWJallu6nYxoyGiA
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=49A541192751890E2FDAD50AA7E2C2F3

<> 2025-08-29T205615.200.json

###

GET http://localhost:18080/api/users/1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDcxOTM0LCJleHAiOjE3NTY0NzU1MzR9.Ip6u5QU_u3vx8BP7SPHNt8zu0ItQWJallu6nYxoyGiA
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=49A541192751890E2FDAD50AA7E2C2F3

<> 2025-08-29T205539.200.json

###

GET http://localhost:18080/api/users/1
Content-Type: application/json
Authorization: Bearer 1eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDcxOTM0LCJleHAiOjE3NTY0NzU1MzR9.Ip6u5QU_u3vx8BP7SPHNt8zu0ItQWJallu6nYxoyGiA
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=488181C52B73FCDB19AEF998F0E10EFB

###

GET http://localhost:18080/api/users/1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDcxOTM0LCJleHAiOjE3NTY0NzU1MzR9.Ip6u5QU_u3vx8BP7SPHNt8zu0ItQWJallu6nYxoyGiA
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=488181C52B73FCDB19AEF998F0E10EFB

<> 2025-08-29T205336.200.json

###

GET http://localhost:18080/api/users/100
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJrb25nIiwidGVuYW50SWQiOjEwMCwiaWF0IjoxNzU2NDcxOTM0LCJleHAiOjE3NTY0NzU1MzR9.Ip6u5QU_u3vx8BP7SPHNt8zu0ItQWJallu6nYxoyGiA
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=488181C52B73FCDB19AEF998F0E10EFB

<> 2025-08-29T205300.200.json

###

POST http://localhost:18080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=488181C52B73FCDB19AEF998F0E10EFB

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

<> 2025-08-29T205214.200.json

###

POST http://localhost:18080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=488181C52B73FCDB19AEF998F0E10EFB

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

<> 2025-08-29T205040.500.json

###

POST http://localhost:18080/api/auth/login
Content-Type: application/json
Content-Length: 67
User-Agent: IntelliJ HTTP Client/IntelliJ IDEA 2025.2.1
Accept-Encoding: br, deflate, gzip, x-gzip
Accept: */*
Cookie: JSESSIONID=488181C52B73FCDB19AEF998F0E10EFB

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

###

POST http://localhost:18080/api/auth/login
Content-Type: application/json
Content-Length: 67
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Cookie: JSESSIONID=4A8E5545F43FFF632748BC5581D4A8A0
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

<> 2025-08-29T201834.403.json

###

POST http://localhost:18080/api/auth/login
Content-Type: application/json
Content-Length: 67
Connection: Keep-Alive
User-Agent: Apache-HttpClient/4.5.14 (Java/17.0.9)
Accept-Encoding: br,deflate,gzip,x-gzip

{
  "username": "kong",
  "password": "123456",
  "tenantId": 100
}

<> 2025-08-29T201738.403.json

###

