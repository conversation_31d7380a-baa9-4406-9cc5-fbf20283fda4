package com.tourism.chat.auth;

import com.tourism.chat.tenant.TenantContext;
import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

/**
 * Filter that authenticates requests using JWT Bearer token.
 */
@Component
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtTokenProvider tokenProvider;

    public JwtAuthenticationFilter(JwtTokenProvider tokenProvider) {
        this.tokenProvider = tokenProvider;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        String contextPath = request.getContextPath();
        // 放行登录/注册/验证码等公开接口（考虑到可能存在 context-path 前缀）
        // 预检请求直接放行
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            filterChain.doFilter(request, response);
            return;
        }
        if (requestURI.equals(contextPath + "/auth/login")
                || requestURI.equals(contextPath + "/auth/register")
                || requestURI.equals(contextPath + "/captchaImage")
                || requestURI.startsWith(contextPath + "/actuator")) {
            filterChain.doFilter(request, response);
            return;
        }

        String header = request.getHeader("Authorization");
        String token = null;
        if (StringUtils.hasText(header) && header.startsWith("Bearer ")) {
            token = header.substring(7);
        }
        if (token != null && tokenProvider.validateToken(token)) {
            Claims claims = tokenProvider.parseClaims(token);
            String username = claims.getSubject();
            Long tenantId = null;
            Object t = claims.get("tenantId");

            if (t instanceof Number n) tenantId = n.longValue();
            else if (t != null) tenantId = Long.parseLong(t.toString());
            if (tenantId != null && TenantContext.getTenantId() == null) {
                TenantContext.setTenantId(tenantId);
            }
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                    new User(username, "", Collections.emptyList()), null, Collections.emptyList());
            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authentication);
        } else {
            // 没有提供 Token 或 Token 无效：不在过滤器里直接拦截，交给 Spring Security 的授权机制处理
            // 这样对于 permitAll 的端点（如 /auth/login）不会被错误拦截
        }
        filterChain.doFilter(request, response);
    }
}

