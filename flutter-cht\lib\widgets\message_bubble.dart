import 'package:flutter/material.dart';
import '../models/gewe_message.dart';

/// 消息气泡组件
class MessageBubble extends StatelessWidget {
  final GeWeMessage message;
  final bool isFromCurrentUser;
  final bool showSenderName;

  const MessageBubble({
    super.key,
    required this.message,
    required this.isFromCurrentUser,
    this.showSenderName = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Row(
        mainAxisAlignment: isFromCurrentUser 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isFromCurrentUser) ...[
            _buildAvatar(),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Column(
              crossAxisAlignment: isFromCurrentUser 
                  ? CrossAxisAlignment.end 
                  : CrossAxisAlignment.start,
              children: [
                if (showSenderName && !isFromCurrentUser)
                  _buildSenderName(),
                _buildMessageBubble(context),
                _buildTimestamp(),
              ],
            ),
          ),
          if (isFromCurrentUser) ...[
            const SizedBox(width: 8),
            _buildAvatar(),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 16,
      backgroundColor: isFromCurrentUser ? Colors.blue[300] : Colors.grey[300],
      child: Text(
        _getAvatarText(),
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildSenderName() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4, left: 8),
      child: Text(
        message.fromWxId ?? 'Unknown',
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isFromCurrentUser 
            ? Theme.of(context).primaryColor 
            : Colors.grey[200],
        borderRadius: BorderRadius.circular(18).copyWith(
          topLeft: isFromCurrentUser ? const Radius.circular(18) : const Radius.circular(4),
          topRight: isFromCurrentUser ? const Radius.circular(4) : const Radius.circular(18),
        ),
      ),
      child: _buildMessageContent(),
    );
  }

  Widget _buildMessageContent() {
    switch (message.messageType) {
      case MessageType.text:
        return Text(
          message.content ?? '',
          style: TextStyle(
            fontSize: 16,
            color: isFromCurrentUser ? Colors.white : Colors.black87,
          ),
        );
      case MessageType.image:
        return _buildMediaMessage(Icons.image, '[图片]');
      case MessageType.voice:
        return _buildMediaMessage(Icons.mic, '[语音]');
      case MessageType.video:
        return _buildMediaMessage(Icons.videocam, '[视频]');
      case MessageType.file:
        return _buildMediaMessage(Icons.attach_file, '[文件]');
      case MessageType.location:
        return _buildMediaMessage(Icons.location_on, '[位置]');
      case MessageType.emoji:
        return _buildMediaMessage(Icons.emoji_emotions, '[表情]');
      case MessageType.system:
        return _buildSystemMessage();
      default:
        return Text(
          message.content ?? '[未知消息类型]',
          style: TextStyle(
            fontSize: 16,
            color: isFromCurrentUser ? Colors.white : Colors.black87,
            fontStyle: FontStyle.italic,
          ),
        );
    }
  }

  Widget _buildMediaMessage(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: isFromCurrentUser ? Colors.white : Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 16,
            color: isFromCurrentUser ? Colors.white : Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildSystemMessage() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.orange[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Colors.orange[700],
          ),
          const SizedBox(width: 4),
          Text(
            message.content ?? '[系统消息]',
            style: TextStyle(
              fontSize: 14,
              color: Colors.orange[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimestamp() {
    if (message.createDateTime == null) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Text(
        _formatTime(message.sendTime!),
        style: TextStyle(
          fontSize: 10,
          color: Colors.grey[500],
        ),
      ),
    );
  }

  String _getAvatarText() {
    final wxId = isFromCurrentUser ? 'Me' : (message.fromWxId ?? '?');
    if (wxId.isEmpty) return '?';
    return wxId.substring(0, 1).toUpperCase();
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return '昨天 ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
      } else {
        return '${time.month}/${time.day} ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
      }
    } else {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}
