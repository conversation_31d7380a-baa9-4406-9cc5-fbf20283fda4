import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../config/app_config.dart';
import '../models/api_response.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/error_interceptor.dart';
import 'interceptors/logging_interceptor.dart';

/// Dio 网络客户端
class DioClient {
  static DioClient? _instance;
  late Dio _dio;

  DioClient._internal() {
    _dio = Dio();
    _setupDio();
    _setupInterceptors();
  }

  static DioClient get instance {
    _instance ??= DioClient._internal();
    return _instance!;
  }

  Dio get dio => _dio;

  /// 配置 Dio 基础设置
  void _setupDio() {
    _dio.options = BaseOptions(
      baseUrl: AppConfig.baseUrl,
      connectTimeout: AppConfig.connectTimeout,
      receiveTimeout: AppConfig.receiveTimeout,
      sendTimeout: AppConfig.sendTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': '${AppConfig.appName}/${AppConfig.appVersion}',
      },
    );
  }

  /// 配置拦截器
  void _setupInterceptors() {
    // 认证拦截器
    _dio.interceptors.add(AuthInterceptor());
    
    // 错误处理拦截器
    _dio.interceptors.add(ErrorInterceptor());
    
    // 日志拦截器（仅在调试模式下）
    if (AppConfig.isDebug && kDebugMode) {
      _dio.interceptors.add(LoggingInterceptor());
    }
  }

  /// GET 请求
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// POST 请求
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// PUT 请求
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// DELETE 请求
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// 文件上传
  Future<ApiResponse<T>> upload<T>(
    String path,
    String filePath, {
    String? fileName,
    Map<String, dynamic>? data,
    ProgressCallback? onSendProgress,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath, filename: fileName),
        ...?data,
      });

      final response = await _dio.post(
        path,
        data: formData,
        onSendProgress: onSendProgress,
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
        ),
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// 处理响应
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    final data = response.data;
    
    if (data is Map<String, dynamic>) {
      // 如果响应已经是 ApiResponse 格式
      if (data.containsKey('code') && data.containsKey('message')) {
        final code = data['code'] as int;
        final message = data['message'] as String;
        final responseData = data['data'];
        
        T? parsedData;
        if (responseData != null && fromJson != null) {
          parsedData = fromJson(responseData);
        } else {
          parsedData = responseData as T?;
        }
        
        return ApiResponse<T>(
          code: code,
          message: message,
          data: parsedData,
          // timestamp: data['timestamp'] as int?,
          // traceId: data['traceId'] as String?,
        );
      }
    }
    
    // 如果响应不是标准格式，包装成 ApiResponse
    T? parsedData;
    if (data != null && fromJson != null) {
      parsedData = fromJson(data);
    } else {
      parsedData = data as T?;
    }
    
    return ApiResponse.success(
      data: parsedData as T,
      message: 'Success',
    );
  }

  /// 处理错误
  ApiResponse<T> _handleError<T>(dynamic error) {
    if (error is DioException) {
      return ApiResponse.failure(
        code: error.response?.statusCode ?? -1,
        message: error.message ?? 'Unknown error',
      );
    }
    
    return ApiResponse.failure(
      code: -1,
      message: error.toString(),
    );
  }

  /// 设置认证 Token
  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  /// 清除认证 Token
  void clearAuthToken() {
    _dio.options.headers.remove('Authorization');
  }
}
