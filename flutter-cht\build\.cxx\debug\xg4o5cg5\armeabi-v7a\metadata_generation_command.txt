                        -HF:\Software\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\new\flutter-cht\build\app\intermediates\cxx\debug\xg4o5cg5\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\new\flutter-cht\build\app\intermediates\cxx\debug\xg4o5cg5\obj\armeabi-v7a
-BF:\new\flutter-cht\build\.cxx\debug\xg4o5cg5\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
-DCMAKE_BUILD_TYPE=debug
                        Build command args: []
                        Version: 2