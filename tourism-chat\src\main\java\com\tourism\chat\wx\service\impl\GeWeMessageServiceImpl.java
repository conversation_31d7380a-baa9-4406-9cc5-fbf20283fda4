package com.tourism.chat.wx.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.chat.wx.entity.GeWeFriend;
import com.tourism.chat.wx.entity.GeWeMessage;
import com.tourism.chat.wx.mappper.GeWeMessageMapper;
import com.tourism.chat.wx.model.WeChatMessage;
import com.tourism.chat.wx.service.GeWeApiService;
import com.tourism.chat.wx.service.GeWeFriendSyncService;
import com.tourism.chat.wx.service.GeWeMessageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * GeWe消息发送服务实现类
 */
@Service
@Slf4j
public class GeWeMessageServiceImpl extends ServiceImpl<GeWeMessageMapper, GeWeMessage> implements GeWeMessageService {

    @Resource
    private GeWeApiService geWeApiService;

    @Resource
    private GeWeFriendSyncService geWeFriendSyncService;

    @Override
    public WeChatMessage sendTextToFriend(String wxId, String content) {
        if (!StringUtils.hasText(wxId) || !StringUtils.hasText(content)) {
            log.error("发送消息参数不能为空: wxId={}, content={}", wxId, content);
            return null;
        }

        // 验证好友是否存在
        GeWeFriend friend = geWeFriendSyncService.getFriendByWxId(wxId);
        if (friend == null) {
            log.error("好友不存在: {}", wxId);
            return null;
        }

        if (Boolean.TRUE.equals(friend.getIsGroup())) {
            log.error("微信ID {} 是群聊，请使用发送群聊消息接口", wxId);
            return null;
        }

        log.info("发送消息给好友: {} ({}), 内容: {}", friend.getNickName(), wxId, content);

        WeChatMessage success = geWeApiService.sendTextMessage(wxId, content, null);
        
        if (success == null) {
            log.info("消息发送成功");
        } else {
            log.error("消息发送失败");
        }
        
        return success;
    }

    @Override
    public WeChatMessage sendTextToGroup(String groupWxId, String content) {
        if (!StringUtils.hasText(groupWxId) || !StringUtils.hasText(content)) {
            log.error("发送群聊消息参数不能为空: groupWxId={}, content={}", groupWxId, content);
            return null;
        }

        // 验证群聊是否存在
        GeWeFriend group = geWeFriendSyncService.getFriendByWxId(groupWxId);
        if (group == null) {
            log.error("群聊不存在: {}", groupWxId);
            return null;
        }

        if (!Boolean.TRUE.equals(group.getIsGroup())) {
            log.error("微信ID {} 不是群聊，请使用发送好友消息接口", groupWxId);
            return null;
        }

        log.info("发送消息给群聊: {} ({}), 内容: {}", group.getNickName(), groupWxId, content);

        WeChatMessage success = geWeApiService.sendTextMessage(groupWxId, content, null);
        
        if (success == null) {
            log.info("群聊消息发送成功");
        } else {
            log.error("群聊消息发送失败");
        }
        
        return success;
    }

    @Override
    public WeChatMessage sendTextToGroupWithAt(String groupWxId, String content, String atWxId) {
        if (!StringUtils.hasText(atWxId)) {
            return sendTextToGroup(groupWxId, content);
        }

        return sendTextToGroupWithMultipleAt(groupWxId, content, atWxId);
    }

    @Override
    public WeChatMessage sendTextToGroupWithMultipleAt(String groupWxId, String content, String atWxIds) {
        if (!StringUtils.hasText(groupWxId) || !StringUtils.hasText(content)) {
            log.error("发送群聊@消息参数不能为空: groupWxId={}, content={}", groupWxId, content);
            return null;
        }

        // 验证群聊是否存在
        GeWeFriend group = geWeFriendSyncService.getFriendByWxId(groupWxId);
        if (group == null) {
            log.error("群聊不存在: {}", groupWxId);
            return null;
        }

        if (!Boolean.TRUE.equals(group.getIsGroup())) {
            log.error("微信ID {} 不是群聊，请使用发送好友消息接口", groupWxId);
            return null;
        }

        String finalContent = content;
        String finalAtWxIds = null;

        // 处理@功能
        if (StringUtils.hasText(atWxIds)) {
            finalAtWxIds = atWxIds;
            
            // 解析@的微信ID，构建@消息内容
            List<String> atWxIdList = Arrays.stream(atWxIds.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .collect(Collectors.toList());

            if (!atWxIdList.isEmpty()) {
                StringBuilder atContent = new StringBuilder();
                
                for (String atWxId : atWxIdList) {
                    GeWeFriend atFriend = geWeFriendSyncService.getFriendByWxId(atWxId);
                    if (atFriend != null) {
                        String displayName = StringUtils.hasText(atFriend.getRemark()) 
                                ? atFriend.getRemark() 
                                : atFriend.getNickName();
                        atContent.append("@").append(displayName).append(" ");
                    } else {
                        log.warn("要@的好友不存在: {}", atWxId);
                        atContent.append("@").append(atWxId).append(" ");
                    }
                }
                
                finalContent = atContent.toString() + content;
            }
        }

        log.info("发送@消息给群聊: {} ({}), 内容: {}, @用户: {}", 
                group.getNickName(), groupWxId, finalContent, atWxIds);

        WeChatMessage message = geWeApiService.sendTextMessage(groupWxId, finalContent, finalAtWxIds);
        
        if (message == null) {
            log.info("群聊@消息发送成功");
        } else {
            log.error("群聊@消息发送失败");
        }
        
        return message;
    }

    @Override
    public Optional<GeWeMessage> findByMessageId(String messageId) {
        if (!StringUtils.hasText(messageId)) {
            return Optional.empty();
        }
        GeWeMessage one = this.lambdaQuery().eq(GeWeMessage::getMessageId, messageId).one();
        if (one != null) {
            return Optional.of(one);
        }
        return Optional.empty();
    }

    @Override
    public void deleteMessagesBefore(LocalDateTime beforeTime) {

        this.lambdaUpdate()
                .lt(GeWeMessage::getCreateTime, beforeTime)
                .remove();
    }

    @Override
    public Page<GeWeMessage> findChatHistory(String wxId1, String wxId2, Pageable pageable) {

        Page<GeWeMessage> page = this.lambdaQuery()
                .eq(GeWeMessage::getFromWxId, wxId1)
                .eq(GeWeMessage::getToWxId, wxId2)
                .or()
                .eq(GeWeMessage::getFromWxId, wxId2)
                .eq(GeWeMessage::getToWxId, wxId1)
                .orderByDesc(GeWeMessage::getCreateTime)
                .page(new Page<>(pageable.getPageNumber(), pageable.getPageSize()));
        return page;
    }

    @Override
    public Page<GeWeMessage> findGroupChatHistory(String groupWxId, Pageable pageable) {
        Page<GeWeMessage> page = this.lambdaQuery()
                .eq(GeWeMessage::getToWxId, groupWxId)
                .eq(GeWeMessage::getIsGroup, true)
                .orderByDesc(GeWeMessage::getCreateTime)
                .page(new Page<>(pageable.getPageNumber(), pageable.getPageSize()));
        return page;
    }

    @Override
    public Page<GeWeMessage> findUserAllMessages(String wxId, Pageable pageable) {
        Page<GeWeMessage> page = this.lambdaQuery()
                .eq(GeWeMessage::getFromWxId, wxId)
                .or()
                .eq(GeWeMessage::getToWxId, wxId)
                .orderByDesc(GeWeMessage::getCreateTime)
                .page(new Page<>(pageable.getPageNumber(), pageable.getPageSize()));
        if (page != null) {
            return page;
        }
        return null;
    }

    @Override
    public Page<GeWeMessage> findMessagesByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        Page<GeWeMessage> page = this.lambdaQuery()
                .ge(GeWeMessage::getCreateTime, startTime)
                .le(GeWeMessage::getCreateTime, endTime)
                .orderByDesc(GeWeMessage::getCreateTime)
                .page(new Page<>(pageable.getPageNumber(), pageable.getPageSize()));
        if (page != null) {
            return page;
        }
        return null;
    }

    @Override
    public long countChatMessages(String wxId1, String wxId2) {
        long count = this.lambdaQuery()
                .eq(GeWeMessage::getFromWxId, wxId1)
                .eq(GeWeMessage::getToWxId, wxId2)
                .or()
                .eq(GeWeMessage::getFromWxId, wxId2)
                .eq(GeWeMessage::getToWxId, wxId1)
                .count();
        return count;
    }

    @Override
    public long countGroupMessages(String groupWxId) {
        long count = this.lambdaQuery()
                .eq(GeWeMessage::getToWxId, groupWxId)
                .eq(GeWeMessage::getIsGroup, true)
                .count();
        return count;
    }

    @Override
    public List<String> findRecentChatContacts(String wxId, Pageable pageable) {
        List<String> recentChats = this.baseMapper.findRecentChatContacts(wxId, new Page<>(pageable.getPageNumber(), pageable.getPageSize()));
        if (recentChats != null && !recentChats.isEmpty()) {
            return recentChats;
        }
        return List.of();
    }

    @Override
    public List<String> findRecentGroups(String wxId, Pageable pageable) {
        List<String> recentGroups = this.baseMapper.findRecentGroups(wxId, new Page<>(pageable.getPageNumber(), pageable.getPageSize()));
        if (recentGroups != null && !recentGroups.isEmpty()) {
            return recentGroups;
        }
        return List.of();
    }

    @Override
    public Optional<GeWeMessage> findById(Long messageId) {
        if (messageId == null) {
            return Optional.empty();
        }
        GeWeMessage one = this.lambdaQuery().eq(GeWeMessage::getId, messageId).one();
        if (one != null) {
            return Optional.of(one);
        }
        return Optional.empty();
    }
}