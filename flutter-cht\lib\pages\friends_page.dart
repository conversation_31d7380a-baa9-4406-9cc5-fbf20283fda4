import 'package:flutter/material.dart';
import '../models/gewe_friend.dart';
import '../services/gewe_friend_service.dart';
import '../widgets/friend_list_item.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_widget.dart';

/// 好友列表页面
class FriendsPage extends StatefulWidget {
  const FriendsPage({super.key});

  @override
  State<FriendsPage> createState() => _FriendsPageState();
}

class _FriendsPageState extends State<FriendsPage>
    with AutomaticKeepAliveClientMixin {
  final GeWeFriendService _friendService = GeWeFriendService();
  final ScrollController _scrollController = ScrollController();

  List<GeWeFriend> _friends = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadFriends();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 加载好友列表
  Future<void> _loadFriends() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final response = await _friendService.getFriendList();

      if (mounted) {
        if (response.isSuccess && response.data != null) {
          setState(() {
            _friends = response.data!;
            _isLoading = false;
          });
        } else {
          setState(() {
            _hasError = true;
            _errorMessage = response.message;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '加载好友列表失败: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// 刷新好友列表
  Future<void> _refreshFriends() async {
    await _loadFriends();
  }

  /// 搜索好友
  void _searchFriends() {
    Navigator.pushNamed(context, '/search', arguments: 'friends');
  }

  /// 打开好友详情
  void _openFriendDetail(GeWeFriend friend) {
    Navigator.pushNamed(context, '/friend-detail', arguments: friend);
  }

  /// 开始聊天
  void _startChat(GeWeFriend friend) {
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: {
        'contactWxId': friend.wxId,
        'contactName': friend.name,
        'isGroup': false,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: RefreshIndicator(onRefresh: _refreshFriends, child: _buildBody()),
      floatingActionButton: FloatingActionButton(
        onPressed: _searchFriends,
        tooltip: '搜索好友',
        child: const Icon(Icons.search),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading && _friends.isEmpty) {
      return const LoadingWidget(message: '正在加载好友列表...');
    }

    if (_hasError && _friends.isEmpty) {
      return EmptyWidget(
        icon: Icons.error_outline,
        title: '加载失败',
        subtitle: _errorMessage,
        actionText: '重试',
        onAction: _loadFriends,
      );
    }

    if (_friends.isEmpty) {
      return const EmptyWidget(
        icon: Icons.people_outline,
        title: '暂无好友',
        subtitle: '点击右下角搜索按钮添加好友',
      );
    }

    return _buildFriendsList();
  }

  Widget _buildFriendsList() {
    // 按首字母分组
    final groupedFriends = _groupFriendsByInitial(_friends);
    final sortedKeys = groupedFriends.keys.toList()..sort();

    return ListView.builder(
      controller: _scrollController,
      itemCount: _calculateItemCount(sortedKeys, groupedFriends),
      itemBuilder: (context, index) {
        return _buildListItem(index, sortedKeys, groupedFriends);
      },
    );
  }

  /// 按首字母分组好友
  Map<String, List<GeWeFriend>> _groupFriendsByInitial(
    List<GeWeFriend> friends,
  ) {
    final Map<String, List<GeWeFriend>> grouped = {};

    for (final friend in friends) {
      String initial = _getInitial(friend);
      if (!grouped.containsKey(initial)) {
        grouped[initial] = [];
      }
      grouped[initial]!.add(friend);
    }

    // 对每组内的好友进行排序
    for (final group in grouped.values) {
      group.sort((a, b) => a.name.compareTo(b.name));
    }

    return grouped;
  }

  /// 获取好友姓名首字母
  String _getInitial(GeWeFriend friend) {
    final name =
        friend.remark?.isNotEmpty == true
            ? friend.remark!
            : friend.nickName ?? friend.wxId;
    if (name.isEmpty) return '#';

    final firstChar = name[0].toUpperCase();
    if (RegExp(r'[A-Z]').hasMatch(firstChar)) {
      return firstChar;
    }
    return '#';
  }

  /// 计算列表项总数
  int _calculateItemCount(
    List<String> sortedKeys,
    Map<String, List<GeWeFriend>> groupedFriends,
  ) {
    int count = 0;
    for (final key in sortedKeys) {
      count += 1; // 分组标题
      count += groupedFriends[key]!.length; // 好友项
    }
    return count;
  }

  /// 构建列表项
  Widget _buildListItem(
    int index,
    List<String> sortedKeys,
    Map<String, List<GeWeFriend>> groupedFriends,
  ) {
    int currentIndex = 0;

    for (final key in sortedKeys) {
      // 分组标题
      if (currentIndex == index) {
        return _buildGroupHeader(key, groupedFriends[key]!.length);
      }
      currentIndex++;

      // 好友项
      final friends = groupedFriends[key]!;
      for (int i = 0; i < friends.length; i++) {
        if (currentIndex == index) {
          return FriendListItem(
            friend: friends[i],
            onTap: () => _openFriendDetail(friends[i]),
            onChatTap: () => _startChat(friends[i]),
          );
        }
        currentIndex++;
      }
    }

    return const SizedBox.shrink();
  }

  /// 构建分组标题
  Widget _buildGroupHeader(String initial, int count) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.grey[100],
      child: Row(
        children: [
          Text(
            initial,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '($count)',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}
