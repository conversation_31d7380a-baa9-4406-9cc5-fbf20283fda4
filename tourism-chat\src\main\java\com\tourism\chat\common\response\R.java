package com.tourism.chat.common.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Common API response wrapper.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class R<T> {
    private int code;
    private String message;
    private T data;

    public static <T> R<T> success(T data) {
        return new R<>(200, "success", data);
    }

    public static <T> R<T> success() {
        return new R<>(200, "success", null);
    }

    public static <T> R<T> fail(int code, String message) {
        return new R<>(code, message, null);
    }

    public static <T> R<T> fail(String message) {
        return new R<>(400, message, null);
    }

    public static <T> R<T> ok(T data) {
        return R.success(data);
    }

    public static <T> R<T> status(int i) {
        return new R<>(i, "success", null);
    }

    public static <T> R<T> fail(int i) {
        return new R<>(i, "fail", null);
    }

    public <T> R<T> body(T result) {
        return new R(200, "success", result);
    }
}

