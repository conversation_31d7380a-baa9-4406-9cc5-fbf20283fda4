<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2d103e7a-926d-4090-bdfe-43765431412e" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/config/JacksonConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/model/WeChatMessage.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/auth/JwtAuthenticationFilter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/auth/JwtAuthenticationFilter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/auth/JwtTokenProvider.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/auth/JwtTokenProvider.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/common/response/R.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/common/response/R.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/controller/GeWeFriendController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/controller/GeWeFriendController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/entity/GeWeMessage.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/entity/GeWeMessage.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/GeWeApiService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/GeWeApiService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/GeWeMessageHistoryService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/GeWeMessageHistoryService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/GeWeMessageService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/GeWeMessageService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/impl/GeWeMessageHistoryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/impl/GeWeMessageHistoryServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/impl/GeWeMessageServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/service/impl/GeWeMessageServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
  </component>
  <component name="HttpClientOnboardingState">{
  &quot;isOnboardingCommentShown&quot;: true
}</component>
  <component name="HttpClientSelectedEnvironments">
    <file url="file://$USER_HOME$/AppData/Local/Temp/whats-new-dir/whats-new.http" environment="test" />
  </component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="F:\.mm2\repository" />
        <option name="userSettingsFile" value="F:\.mm2\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="31xbdzvQ6cgpDozD4LVkZp1s30G" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;HTTP Request.generated-requests | #3 (1).executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | #4.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.generated-requests | #5.executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.DemoApplication.executor&quot;: &quot;Debug&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/new/tourism-chat/src/main/java/com/tourism/chat/wx&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\new\tourism-chat\src\main\java\com\tourism\chat\wx" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationStatuses">
      <map>
        <entry key="HttpClient.HttpRequestRunConfigurationType">
          <value>
            <map>
              <entry key="generated-requests | #3 (1)" value="STOPPED" />
              <entry key="generated-requests | #4" value="STOPPED" />
              <entry key="generated-requests | #5" value="STOPPED" />
            </map>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.DemoApplication">
    <configuration name="generated-requests | #3 (1)" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#3" index="3" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="generated-requests | #3" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" index="3" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="generated-requests | #4" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#4" index="4" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="generated-requests | #5" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#5" index="5" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="DemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="tourism-chat" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tourism.chat.DemoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP Request.generated-requests | #3 (1)" />
        <item itemvalue="HTTP Request.generated-requests | #4" />
        <item itemvalue="HTTP Request.generated-requests | #5" />
        <item itemvalue="HTTP Request.generated-requests | #3" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-bf35d07a577b-intellij.indexing.shared.core-IU-252.25557.131" />
        <option value="bundled-js-predefined-d6986cc7102b-b598e85cdad2-JavaScript-IU-252.25557.131" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2d103e7a-926d-4090-bdfe-43765431412e" name="Changes" comment="" />
      <created>1756468762467</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756468762467</updated>
      <workItem from="1756468763619" duration="1158000" />
      <workItem from="1756471029537" duration="109000" />
      <workItem from="1756471160084" duration="10681000" />
      <workItem from="1756485801326" duration="7712000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/tourism/chat/wx/controller/GeWeFriendController.java</url>
          <line>253</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>