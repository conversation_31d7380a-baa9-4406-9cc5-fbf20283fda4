-- Merging decision tree log ---
application
INJECTED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26ef6226133e1074d1614a14900ebe56\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26ef6226133e1074d1614a14900ebe56\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ef31db8a4712a47766656f2baf19e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ef31db8a4712a47766656f2baf19e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml
manifest
ADDED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:flutter_local_notifications] F:\new\flutter-cht\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8897285d62dbd82089ccc1f891634ad2\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ea16852286969f42399b567266332c6\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfb75ee02adf6d8a7ae781b14eabedac\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5dc077c0afa2b78ccc4ffc00061b392\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2270e7fedb9f0c98ffcc5406d455b039\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c47b75f4f363f6a78f247430d5888b8d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e093f000a25401d602ea84732a32f13b\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b44bf579eb82e760cb45cb0729387378\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16ab2fd971583f017f2dfe32762a270a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05c744520a1b29a90556c555d7121194\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57624c27892b6e250c457636352bc6db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b1c3dfb5587faebe24f6d425a80c9f2\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc7e588d41cc46730830a88dc9d145cc\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89a5bb2316061f70da7a889dfd9041e1\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b0b29714a1d6fecb61db956d250ea7e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a8c874d7815f4455848a0dd48b7322b\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26ef6226133e1074d1614a14900ebe56\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d90922d802f6d5e34e710bd5682e85c9\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd29627b35ed5a8f66a5cf6e588c4701\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ef31db8a4712a47766656f2baf19e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4e9efc36df39c7b01f11349936163dd\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61bf4042333f033b306ff30249e0489b\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68a9ae614bf522d99982d513a6e1cc97\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:39:5-44:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from F:\new\flutter-cht\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml:6:5-66
	android:name
		ADDED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml
INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_local_notifications] F:\new\flutter-cht\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] F:\new\flutter-cht\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8897285d62dbd82089ccc1f891634ad2\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8897285d62dbd82089ccc1f891634ad2\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ea16852286969f42399b567266332c6\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ea16852286969f42399b567266332c6\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfb75ee02adf6d8a7ae781b14eabedac\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfb75ee02adf6d8a7ae781b14eabedac\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5dc077c0afa2b78ccc4ffc00061b392\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5dc077c0afa2b78ccc4ffc00061b392\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2270e7fedb9f0c98ffcc5406d455b039\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2270e7fedb9f0c98ffcc5406d455b039\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c47b75f4f363f6a78f247430d5888b8d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c47b75f4f363f6a78f247430d5888b8d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e093f000a25401d602ea84732a32f13b\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e093f000a25401d602ea84732a32f13b\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b44bf579eb82e760cb45cb0729387378\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b44bf579eb82e760cb45cb0729387378\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16ab2fd971583f017f2dfe32762a270a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16ab2fd971583f017f2dfe32762a270a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05c744520a1b29a90556c555d7121194\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05c744520a1b29a90556c555d7121194\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57624c27892b6e250c457636352bc6db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57624c27892b6e250c457636352bc6db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b1c3dfb5587faebe24f6d425a80c9f2\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b1c3dfb5587faebe24f6d425a80c9f2\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc7e588d41cc46730830a88dc9d145cc\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc7e588d41cc46730830a88dc9d145cc\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89a5bb2316061f70da7a889dfd9041e1\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89a5bb2316061f70da7a889dfd9041e1\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b0b29714a1d6fecb61db956d250ea7e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b0b29714a1d6fecb61db956d250ea7e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a8c874d7815f4455848a0dd48b7322b\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a8c874d7815f4455848a0dd48b7322b\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26ef6226133e1074d1614a14900ebe56\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26ef6226133e1074d1614a14900ebe56\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d90922d802f6d5e34e710bd5682e85c9\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d90922d802f6d5e34e710bd5682e85c9\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd29627b35ed5a8f66a5cf6e588c4701\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd29627b35ed5a8f66a5cf6e588c4701\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ef31db8a4712a47766656f2baf19e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ef31db8a4712a47766656f2baf19e9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4e9efc36df39c7b01f11349936163dd\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4e9efc36df39c7b01f11349936163dd\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61bf4042333f033b306ff30249e0489b\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61bf4042333f033b306ff30249e0489b\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68a9ae614bf522d99982d513a6e1cc97\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68a9ae614bf522d99982d513a6e1cc97\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\new\flutter-cht\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] F:\new\flutter-cht\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:flutter_local_notifications] F:\new\flutter-cht\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:flutter_local_notifications] F:\new\flutter-cht\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
	android:name
		ADDED from [:flutter_local_notifications] F:\new\flutter-cht\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71b3022911db3108d3b570e0e1205f4c\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.im_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.im_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49992c126bf9275512e9c8be998f19ff\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26ef6226133e1074d1614a14900ebe56\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26ef6226133e1074d1614a14900ebe56\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89e8edadf72981be3f41104e2c29e6cc\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\751949e02d436246ef8b559c4a91edb3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
